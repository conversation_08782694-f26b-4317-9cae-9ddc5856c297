//
//  PhotoManager.swift
//  PhotoUncle
//
//  Created by wtb on 2025/7/12.
//

import Foundation
import SwiftUI

// MARK: - PhotoManager
class PhotoManager: ObservableObject {
    @Published var photos: [PhotoItem] = []
    @Published var selectedPhoto: PhotoItem?
    @Published var currentDirectory: URL?
    @Published var isLoading: Bool = false

    private let supportedImageTypes = ["jpg", "jpeg", "png", "gif", "bmp", "tiff", "tif", "heic", "webp"]

    func loadPhotosFromDirectory(_ url: URL) {
        isLoading = true
        currentDirectory = url
        photos.removeAll()
        selectedPhoto = nil

        // 清理缩略图缓存
        ThumbnailGenerator.shared.clearCache()

        guard url.startAccessingSecurityScopedResource() else {
            print("无法访问目录: \(url)")
            isLoading = false
            return
        }

        defer {
            url.stopAccessingSecurityScopedResource()
            isLoading = false
        }

        do {
            let fileURLs = try FileManager.default.contentsOfDirectory(
                at: url,
                includingPropertiesForKeys: [.isRegularFileKey, .contentTypeKey],
                options: [.skipsHiddenFiles]
            )

            let imageURLs = fileURLs.filter { url in
                let pathExtension = url.pathExtension.lowercased()
                return supportedImageTypes.contains(pathExtension)
            }

            // 按文件名排序
            let sortedImageURLs = imageURLs.sorted { $0.lastPathComponent < $1.lastPathComponent }

            photos = sortedImageURLs.map { PhotoItem(url: $0) }

            // 选择第一张图片
            if !photos.isEmpty {
                selectedPhoto = photos[0]
            }

        } catch {
            print("读取目录失败: \(error)")
        }
    }

    func selectPhoto(_ photo: PhotoItem) {
        selectedPhoto = photo
    }

    func selectNextPhoto() {
        guard let currentPhoto = selectedPhoto,
              let currentIndex = photos.firstIndex(of: currentPhoto),
              currentIndex < photos.count - 1 else { return }

        selectedPhoto = photos[currentIndex + 1]
    }

    func selectPreviousPhoto() {
        guard let currentPhoto = selectedPhoto,
              let currentIndex = photos.firstIndex(of: currentPhoto),
              currentIndex > 0 else { return }

        selectedPhoto = photos[currentIndex - 1]
    }

    func getCurrentPhotoIndex() -> Int? {
        guard let currentPhoto = selectedPhoto else { return nil }
        return photos.firstIndex(of: currentPhoto)
    }

    func getPhotoCount() -> Int {
        return photos.count
    }

    func refreshCurrentDirectory() {
        guard let currentDirectory = currentDirectory else { return }
        loadPhotosFromDirectory(currentDirectory)
    }
}