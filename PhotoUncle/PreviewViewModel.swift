//
//  PreviewViewModel.swift
//  PhotoUncle
//
//  Created by wtb on 2025/7/12.
//

import Foundation
import SwiftUI

// MARK: - PreviewViewModel
class PreviewViewModel: ObservableObject {
    @Published var scale: CGFloat = 1.0
    @Published var offset: CGSize = .zero
    @Published var lastOffset: CGSize = .zero
    @Published var currentPhoto: PhotoItem?

    func setPhoto(_ photo: PhotoItem?) {
        if currentPhoto != photo {
            currentPhoto = photo
            resetZoomAndPosition()
        }
    }

    func updateScale(_ newScale: CGFloat) {
        scale = max(0.1, min(10.0, newScale))
    }

    func updateOffset(_ newOffset: CGSize) {
        offset = newOffset
    }

    func updateLastOffset(_ newOffset: CGSize) {
        lastOffset = newOffset
    }

    func resetZoomAndPosition() {
        withAnimation(.easeInOut(duration: 0.3)) {
            scale = 1.0
            offset = .zero
            lastOffset = .zero
        }
    }

    func zoomIn() {
        withAnimation(.easeInOut(duration: 0.2)) {
            scale = min(10.0, scale * 1.5)
        }
    }

    func zoomOut() {
        withAnimation(.easeInOut(duration: 0.2)) {
            scale = max(0.1, scale / 1.5)
        }
    }

    func fitToScreen() {
        withAnimation(.easeInOut(duration: 0.3)) {
            scale = 1.0
            offset = .zero
            lastOffset = .zero
        }
    }

    var canZoomIn: Bool {
        return scale < 10.0
    }

    var canZoomOut: Bool {
        return scale > 0.1
    }

    var zoomPercentage: Int {
        return Int(scale * 100)
    }
}