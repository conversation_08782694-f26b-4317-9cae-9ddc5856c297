//
//  ThumbListView.swift
//  PhotoUncle
//
//  Created by wtb on 2025/7/12.
//

import SwiftUI

struct ThumbListView: View {
    @ObservedObject var viewModel: ThumbListViewModel

    var body: some View {
        Group {
            if !viewModel.photos.isEmpty {
                ThumbnailPanel(
                    images: thumbnailImages,
                    position: thumbnailPanelPosition,
                    selectedIndex: selectedIndex,
                    onSelect: { index in
                        if index < viewModel.photos.count {
                            viewModel.selectPhoto(viewModel.photos[index])
                        }
                    }
                )
            } else {
                EmptyView()
            }
        }
    }

    // MARK: - Private Properties

    private var thumbnailImages: [NSImage] {
        return viewModel.photos.compactMap { photo in
            photo.thumbnail
        }
    }

    private var thumbnailPanelPosition: ThumbnailPanelPosition {
        switch viewModel.thumbnailPosition {
        case .top:
            return .top
        case .bottom:
            return .bottom
        case .left:
            return .left
        case .right:
            return .right
        }
    }

    private var selectedIndex: Int {
        guard let selectedPhoto = viewModel.selectedPhoto,
              let index = viewModel.photos.firstIndex(of: selectedPhoto) else {
            return -1
        }
        return index
    }
}

#Preview {
    // 创建预览数据
    let photoLoader = PhotoLoader()
    let thumbListViewModel = ThumbListViewModel(photoLoader: photoLoader)

    return ThumbListView(viewModel: thumbListViewModel)
        .frame(width: 400, height: 100)
}