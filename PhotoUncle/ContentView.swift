//
//  ContentView.swift
//  PhotoUncle
//
//  Created by wtb on 2025/7/12.
//

import SwiftUI

// MARK: - ThumbnailGenerator
class ThumbnailGenerator {
    static let shared = ThumbnailGenerator()
    private let thumbnailSize: CGSize = CGSize(width: 200, height: 200)
    private var cache: [URL: NSImage] = [:]

    private init() {}

    func generateThumbnail(for url: URL) -> NSImage? {
        // 检查缓存
        if let cachedThumbnail = cache[url] {
            return cachedThumbnail
        }

        guard let originalImage = NSImage(contentsOf: url) else {
            return nil
        }

        let thumbnail = createSquareThumbnail(from: originalImage, size: thumbnailSize)

        // 缓存缩略图
        if let thumbnail = thumbnail {
            cache[url] = thumbnail
        }

        return thumbnail
    }

    private func createSquareThumbnail(from image: NSImage, size: CGSize) -> NSImage? {
        guard let cgImage = image.cgImage(forProposedRect: nil, context: nil, hints: nil) else {
            return nil
        }

        let originalWidth = CGFloat(cgImage.width)
        let originalHeight = CGFloat(cgImage.height)

        // 计算正方形裁剪区域（居中取样）
        let cropSize = min(originalWidth, originalHeight)
        let cropX = (originalWidth - cropSize) / 2
        let cropY = (originalHeight - cropSize) / 2

        let cropRect = CGRect(x: cropX, y: cropY, width: cropSize, height: cropSize)

        guard let croppedCGImage = cgImage.cropping(to: cropRect) else {
            return nil
        }

        // 创建缩略图
        let thumbnailImage = NSImage(size: size)
        thumbnailImage.lockFocus()

        let context = NSGraphicsContext.current?.cgContext
        context?.interpolationQuality = .high

        let drawRect = CGRect(origin: .zero, size: size)
        context?.draw(croppedCGImage, in: drawRect)

        thumbnailImage.unlockFocus()

        return thumbnailImage
    }

    func clearCache() {
        cache.removeAll()
    }

    func removeCachedThumbnail(for url: URL) {
        cache.removeValue(forKey: url)
    }
}

// MARK: - PhotoItem Model
struct PhotoItem: Identifiable, Hashable {
    let id = UUID()
    let url: URL
    let name: String
    let fileSize: Int64
    let dateCreated: Date

    init(url: URL) {
        self.url = url
        self.name = url.lastPathComponent

        // 获取文件属性
        let attributes = try? FileManager.default.attributesOfItem(atPath: url.path)
        self.fileSize = attributes?[.size] as? Int64 ?? 0
        self.dateCreated = attributes?[.creationDate] as? Date ?? Date()
    }

    var thumbnail: NSImage? {
        return ThumbnailGenerator.shared.generateThumbnail(for: url)
    }

    var image: NSImage? {
        return NSImage(contentsOf: url)
    }

    var fileSizeString: String {
        let formatter = ByteCountFormatter()
        formatter.allowedUnits = [.useKB, .useMB, .useGB]
        formatter.countStyle = .file
        return formatter.string(fromByteCount: fileSize)
    }
}



// MARK: - Thumbnail Position
enum ThumbnailPosition: String, CaseIterable {
    case top = "顶部"
    case bottom = "底部"
    case left = "左侧"
    case right = "右侧"

    var icon: String {
        switch self {
        case .top: return "rectangle.split.1x2"
        case .bottom: return "rectangle.split.1x2"
        case .left: return "rectangle.split.2x1"
        case .right: return "rectangle.split.2x1"
        }
    }
}

// MARK: - Main Content View
struct ContentView: View {
    @StateObject private var photoLoader = PhotoLoader()
    @StateObject private var thumbListViewModel: ThumbListViewModel
    @StateObject private var previewViewModel = PreviewViewModel()
    @State private var showingDirectoryPicker = false

    init() {
        let loader = PhotoLoader()
        let thumbViewModel = ThumbListViewModel(photoLoader: loader)
        _photoLoader = StateObject(wrappedValue: loader)
        _thumbListViewModel = StateObject(wrappedValue: thumbViewModel)
    }

    var body: some View {
        VStack(spacing: 0) {
            // 工具栏
            toolbar

            // 主内容区域
            mainContent
        }
        .fileImporter(
            isPresented: $showingDirectoryPicker,
            allowedContentTypes: [.folder],
            allowsMultipleSelection: false
        ) { result in
            switch result {
            case .success(let urls):
                if let url = urls.first {
                    photoLoader.loadPhotosFromDirectory(url)
                }
            case .failure(let error):
                print("选择目录失败: \(error)")
            }
        }
        .onKeyPress(.leftArrow) {
            thumbListViewModel.selectPreviousPhoto()
            return .handled
        }
        .onKeyPress(.rightArrow) {
            thumbListViewModel.selectNextPhoto()
            return .handled
        }
        .onKeyPress(.space) {
            thumbListViewModel.toggleThumbnailCollapse()
            return .handled
        }
        .onChange(of: photoLoader.photos) { _ in
            thumbListViewModel.updateSelection()
        }
        .onChange(of: thumbListViewModel.selectedPhoto) { newPhoto in
            previewViewModel.setPhoto(newPhoto)
        }
    }

    // MARK: - Toolbar
    private var toolbar: some View {
        HStack {
            // 打开目录按钮
            Button("选择目录") {
                showingDirectoryPicker = true
            }
            .buttonStyle(.borderedProminent)

            Spacer()

            // 显示加载状态
            if photoLoader.isLoading {
                ProgressView()
                    .scaleEffect(0.8)
                Text("加载中...")
                    .foregroundColor(.secondary)
            }

            // 显示图片数量和当前位置
            if !thumbListViewModel.photos.isEmpty {
                if let currentIndex = thumbListViewModel.getCurrentPhotoIndex() {
                    Text("\(currentIndex + 1) / \(thumbListViewModel.getPhotoCount())")
                        .foregroundColor(.secondary)
                        .font(.caption)
                }
            }

            Spacer()

            // 缩略图位置选择
            if !thumbListViewModel.photos.isEmpty {
                Menu {
                    ForEach(ThumbnailPosition.allCases, id: \.self) { position in
                        Button(action: {
                            thumbListViewModel.setThumbnailPosition(position)
                        }) {
                            HStack {
                                Image(systemName: position.icon)
                                Text(position.rawValue)
                                if thumbListViewModel.thumbnailPosition == position {
                                    Image(systemName: "checkmark")
                                }
                            }
                        }
                    }
                } label: {
                    HStack {
                        Image(systemName: thumbListViewModel.thumbnailPosition.icon)
                        Text("缩略图位置")
                    }
                }
                .menuStyle(.borderlessButton)

                // 折叠/展开缩略图
                Button(action: {
                    thumbListViewModel.toggleThumbnailCollapse()
                }) {
                    Image(systemName: thumbListViewModel.isThumbnailCollapsed ? "sidebar.left" : "sidebar.right")
                }
                .help(thumbListViewModel.isThumbnailCollapsed ? "展开缩略图" : "折叠缩略图")
            }
        }
        .padding()
        .background(Color(NSColor.controlBackgroundColor))
    }

    // MARK: - Main Content
    private var mainContent: some View {
        Group {
            if thumbListViewModel.photos.isEmpty && !photoLoader.isLoading {
                // 空状态
                VStack(spacing: 20) {
                    Image(systemName: "photo.on.rectangle.angled")
                        .font(.system(size: 64))
                        .foregroundColor(.secondary)

                    Text("选择一个包含图片的文件夹开始使用")
                        .font(.title2)
                        .foregroundColor(.secondary)

                    Button("选择目录") {
                        showingDirectoryPicker = true
                    }
                    .buttonStyle(.borderedProminent)
                    .controlSize(.large)
                }
                .frame(maxWidth: .infinity, maxHeight: .infinity)
            } else {
                // 有图片时的布局
                photoLayout
            }
        }
    }

    // MARK: - Photo Layout
    private var photoLayout: some View {
        Group {
            switch thumbListViewModel.thumbnailPosition {
            case .top:
                VStack(spacing: 0) {
                    if !thumbListViewModel.isThumbnailCollapsed {
                        thumbnailList
                            .frame(height: 120)
                    }
                    Divider()
                    mainImageView
                }
            case .bottom:
                VStack(spacing: 0) {
                    mainImageView
                    Divider()
                    if !thumbListViewModel.isThumbnailCollapsed {
                        thumbnailList
                            .frame(height: 120)
                    }
                }
            case .left:
                HStack(spacing: 0) {
                    if !thumbListViewModel.isThumbnailCollapsed {
                        thumbnailList
                            .frame(width: 200)
                    }
                    Divider()
                    mainImageView
                }
            case .right:
                HStack(spacing: 0) {
                    mainImageView
                    Divider()
                    if !thumbListViewModel.isThumbnailCollapsed {
                        thumbnailList
                            .frame(width: 200)
                    }
                }
            }
        }
    }

    // MARK: - Thumbnail List
    private var thumbnailList: some View {
        ScrollView(thumbListViewModel.thumbnailPosition == .left || thumbListViewModel.thumbnailPosition == .right ? .vertical : .horizontal) {
            LazyVGrid(
                columns: thumbnailGridColumns,
                spacing: 8
            ) {
                ForEach(thumbListViewModel.photos) { photo in
                    ThumbnailView(
                        photo: photo,
                        isSelected: thumbListViewModel.selectedPhoto?.id == photo.id
                    ) {
                        thumbListViewModel.selectPhoto(photo)
                    }
                }
            }
            .padding(8)
        }
        .background(Color(NSColor.controlBackgroundColor))
    }

    private var thumbnailGridColumns: [GridItem] {
        switch thumbListViewModel.thumbnailPosition {
        case .top, .bottom:
            return [GridItem(.adaptive(minimum: 100, maximum: 120), spacing: 8)]
        case .left, .right:
            return Array(repeating: GridItem(.flexible(), spacing: 8), count: 2)
        }
    }

    // MARK: - Main Image View
    private var mainImageView: some View {
        Group {
            if let selectedPhoto = thumbListViewModel.selectedPhoto {
                MainImageView(photo: selectedPhoto, viewModel: previewViewModel)
            } else {
                VStack {
                    Image(systemName: "photo")
                        .font(.system(size: 64))
                        .foregroundColor(.secondary)
                    Text("选择一张图片查看")
                        .foregroundColor(.secondary)
                }
                .frame(maxWidth: .infinity, maxHeight: .infinity)
            }
        }
        .background(Color.black)
    }
}

// MARK: - Thumbnail View
struct ThumbnailView: View {
    let photo: PhotoItem
    let isSelected: Bool
    let onTap: () -> Void

    var body: some View {
        Button(action: onTap) {
            Group {
                if let thumbnail = photo.thumbnail {
                    Image(nsImage: thumbnail)
                        .resizable()
                        .aspectRatio(contentMode: .fill)
                } else {
                    Image(systemName: "photo")
                        .foregroundColor(.secondary)
                        .frame(width: 100, height: 100)
                }
            }
            .frame(width: 100, height: 100)
            .clipped()
            .cornerRadius(8)
            .overlay(
                RoundedRectangle(cornerRadius: 8)
                    .stroke(isSelected ? Color.accentColor : Color.clear, lineWidth: 3)
            )
            .shadow(radius: isSelected ? 4 : 2)
        }
        .buttonStyle(.plain)
        .help(photo.name)
    }
}

// MARK: - Main Image View
struct MainImageView: View {
    let photo: PhotoItem
    @ObservedObject var viewModel: PreviewViewModel

    var body: some View {
        GeometryReader { geometry in
            Group {
                if let image = photo.image {
                    Image(nsImage: image)
                        .resizable()
                        .aspectRatio(contentMode: .fit)
                        .scaleEffect(viewModel.scale)
                        .offset(viewModel.offset)
                        .gesture(
                            SimultaneousGesture(
                                // 缩放手势
                                MagnificationGesture()
                                    .onChanged { value in
                                        viewModel.updateScale(max(0.1, min(10.0, value)))
                                    },
                                // 拖拽手势
                                DragGesture()
                                    .onChanged { value in
                                        let newOffset = CGSize(
                                            width: viewModel.lastOffset.width + value.translation.width,
                                            height: viewModel.lastOffset.height + value.translation.height
                                        )
                                        viewModel.updateOffset(newOffset)
                                    }
                                    .onEnded { _ in
                                        viewModel.updateLastOffset(viewModel.offset)
                                    }
                            )
                        )
                        .onTapGesture(count: 2) {
                            // 双击重置缩放和位置
                            viewModel.resetZoomAndPosition()
                        }
                } else {
                    VStack {
                        Image(systemName: "exclamationmark.triangle")
                            .font(.system(size: 48))
                            .foregroundColor(.orange)
                        Text("无法加载图片")
                            .foregroundColor(.secondary)
                    }
                }
            }
            .frame(maxWidth: .infinity, maxHeight: .infinity)
            .clipped()
        }
    }
}

#Preview {
    ContentView()
}
