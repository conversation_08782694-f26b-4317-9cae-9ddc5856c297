//
//  ThumbnailPanel.swift
//  PhotoUncle
//
//  Created by wtb on 2025/7/12.
//
enum ThumbnailPanelPosition {
    case top, bottom, left, right
}
struct ThumbnailPanel: View {
    let images: [NSImage]
    let position: ThumbnailPanelPosition
    let selectedIndex: Int
    let onSelect: (Int) -> Void

    var isHorizontal: Bool {
        position == .top || position == .bottom
    }

    var body: some View {
        ScrollView(isHorizontal ? .horizontal : .vertical, showsIndicators: true) {
            layoutView()
                .padding(4)
        }
        .frame(
            maxWidth: isHorizontal ? .infinity : 100,
            maxHeight: isHorizontal ? 100 : .infinity
        )
        .background(Color.gray.opacity(0.1))
    }

    @ViewBuilder
    private func layoutView() -> some View {
        let layout = isHorizontal ? AnyLayout(HStackLayout(spacing: 8)) :
                                    AnyLayout(VStackLayout(spacing: 8))

        layout {
            ForEach(images.indices, id: \.self) { i in
                let image = images[i]
                Image(nsImage: image)
                    .resizable()
                    .aspectRatio(contentMode: .fill)
                    .frame(width: 80, height: 80)
                    .clipped()
                    .overlay(
                        RoundedRectangle(cornerRadius: 4)
                            .stroke(i == selectedIndex ? Color.blue : Color.clear, lineWidth: 2)
                    )
                    .onTapGesture {
                        onSelect(i)
                    }
            }
        }
    }
}
